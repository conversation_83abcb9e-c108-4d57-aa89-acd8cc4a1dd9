import axios from "axios"

// ==================== 集合管理 API ====================

/**
 * 获取API测试集合列表
 * @param {string} projectId - 项目ID
 * @param {string} urlArgs - URL参数
 */
export const getApiTestCollectionsApi = (projectId, urlArgs = '') => {
  let reqUrl = `/api/project/${projectId}/api-test/collections/`
  if (urlArgs) {
    reqUrl = reqUrl + '?' + urlArgs
  }
  return axios.get(reqUrl)
}

/**
 * 获取API测试集合树形结构
 * @param {string} projectId - 项目ID
 */
export const getApiTestCollectionTreeApi = (projectId) => {
  return axios.get(`/api/project/${projectId}/api-test/collections/tree/`)
}

/**
 * 获取API测试集合树形结构V2
 * @param {string} projectId - 项目ID
 */
export const getApiTestCollectionTreeV2Api = (projectId) => {
  return axios.get(`/api/project/${projectId}/api-test/collections/tree_V2/`)
}

/**
 * 创建API测试集合
 * @param {string} projectId - 项目ID
 * @param {object} data - 集合数据
 */
export const createApiTestCollectionApi = (projectId, data) => {
  return axios.post(`/api/project/${projectId}/api-test/collections/`, data)
}

/**
 * 获取API测试集合详情
 * @param {string} projectId - 项目ID
 * @param {string} collectionId - 集合ID
 */
export const getApiTestCollectionDetailApi = (projectId, collectionId) => {
  return axios.get(`/api/project/${projectId}/api-test/collections/${collectionId}/`)
}

/**
 * 更新API测试集合
 * @param {string} projectId - 项目ID
 * @param {string} collectionId - 集合ID
 * @param {object} data - 更新数据
 */
export const updateApiTestCollectionApi = (projectId, collectionId, data) => {
  return axios.put(`/api/project/${projectId}/api-test/collections/${collectionId}/`, data)
}

/**
 * 删除API测试集合
 * @param {string} projectId - 项目ID
 * @param {string} collectionId - 集合ID
 */
export const deleteApiTestCollectionApi = (projectId, collectionId) => {
  return axios.delete(`/api/project/${projectId}/api-test/collections/${collectionId}/`)
}

// ==================== 测试用例管理 API ====================

/**
 * 获取API测试用例列表
 * @param {string} projectId - 项目ID
 * @param {string} urlArgs - URL参数
 */
export const getApiTestCasesApi = (projectId, urlArgs = '') => {
  let reqUrl = `/api/project/${projectId}/api-test/cases/`
  if (urlArgs) {
    reqUrl = reqUrl + '?' + urlArgs
  }
  return axios.get(reqUrl)
}

/**
 * 创建API测试用例
 * @param {string} projectId - 项目ID
 * @param {object} data - 用例数据
 */
export const createApiTestCaseApi = (projectId, data) => {
  return axios.post(`/api/project/${projectId}/api-test/cases/`, data)
}

/**
 * 获取API测试用例详情
 * @param {string} projectId - 项目ID
 * @param {string} caseId - 用例ID
 */
export const getApiTestCaseDetailApi = (projectId, caseId) => {
  return axios.get(`/api/project/${projectId}/api-test/cases/${caseId}/`)
}

/**
 * 更新API测试用例
 * @param {string} projectId - 项目ID
 * @param {string} caseId - 用例ID
 * @param {object} data - 更新数据
 */
export const updateApiTestCaseApi = (projectId, caseId, data) => {
  return axios.put(`/api/project/${projectId}/api-test/cases/${caseId}/`, data)
}

/**
 * 删除API测试用例
 * @param {string} projectId - 项目ID
 * @param {string} caseId - 用例ID
 */
export const deleteApiTestCaseApi = (projectId, caseId) => {
  return axios.delete(`/api/project/${projectId}/api-test/cases/${caseId}/`)
}

/**
 * 执行API测试用例
 * @param {string} projectId - 项目ID
 * @param {string} caseId - 用例ID
 * @param {object} data - 执行参数
 */
export const executeApiTestCaseApi = (projectId, caseId, data = {}) => {
  return axios.post(`/api/project/${projectId}/api-test/cases/${caseId}/execute/`, data)
}

// ==================== 环境管理 API ====================

/**
 * 获取API测试环境列表
 * @param {string} projectId - 项目ID
 * @param {string} urlArgs - URL参数
 */
export const getApiTestEnvironmentsApi = (projectId, urlArgs = '') => {
  let reqUrl = `/api/project/${projectId}/api-test/environments/`
  if (urlArgs) {
    reqUrl = reqUrl + '?' + urlArgs
  }
  return axios.get(reqUrl)
}

/**
 * 创建API测试环境
 * @param {string} projectId - 项目ID
 * @param {object} data - 环境数据
 */
export const createApiTestEnvironmentApi = (projectId, data) => {
  return axios.post(`/api/project/${projectId}/api-test/environments/`, data)
}

/**
 * 获取API测试环境详情
 * @param {string} projectId - 项目ID
 * @param {string} envId - 环境ID
 */
export const getApiTestEnvironmentDetailApi = (projectId, envId) => {
  return axios.get(`/api/project/${projectId}/api-test/environments/${envId}/`)
}

/**
 * 更新API测试环境
 * @param {string} projectId - 项目ID
 * @param {string} envId - 环境ID
 * @param {object} data - 更新数据
 */
export const updateApiTestEnvironmentApi = (projectId, envId, data) => {
  return axios.put(`/api/project/${projectId}/api-test/environments/${envId}/`, data)
}

/**
 * 删除API测试环境
 * @param {string} projectId - 项目ID
 * @param {string} envId - 环境ID
 */
export const deleteApiTestEnvironmentApi = (projectId, envId) => {
  return axios.delete(`/api/project/${projectId}/api-test/environments/${envId}/`)
}

// ==================== 执行历史 API ====================

/**
 * 获取API测试执行历史列表
 * @param {string} projectId - 项目ID
 * @param {string} urlArgs - URL参数
 */
export const getApiTestHistoryApi = (projectId, urlArgs = '') => {
  let reqUrl = `/api/project/${projectId}/api-test/history/`
  if (urlArgs) {
    reqUrl = reqUrl + '?' + urlArgs
  }
  return axios.get(reqUrl)
}

/**
 * 获取API测试执行历史详情
 * @param {string} projectId - 项目ID
 * @param {string} historyId - 历史记录ID
 */
export const getApiTestHistoryDetailApi = (projectId, historyId) => {
  return axios.get(`/api/project/${projectId}/api-test/history/${historyId}/`)
}

// ==================== 代码片段生成 API ====================

/**
 * 生成API测试用例代码片段
 * @param {string} projectId - 项目ID
 * @param {string} caseId - 用例ID
 * @param {object} data - 生成参数
 */
export const generateCodeSnippetApi = (projectId, caseId, data) => {
  return axios.post(`/api/project/${projectId}/api-test/cases/${caseId}/code-snippet/`, data)
}

// ==================== 测试场景管理 API ====================

/**
 * 获取API测试场景列表
 * @param {string} projectId - 项目ID
 * @param {string} urlArgs - URL参数
 */
export const getApiTestScenesApi = (projectId, urlArgs = '') => {
  let reqUrl = `/api/project/${projectId}/api-test/scenes/`
  if (urlArgs) {
    reqUrl = reqUrl + '?' + urlArgs
  }
  return axios.get(reqUrl)
}

/**
 * 创建API测试场景
 * @param {string} projectId - 项目ID
 * @param {object} data - 场景数据
 */
export const createApiTestSceneApi = (projectId, data) => {
  return axios.post(`/api/project/${projectId}/api-test/scenes/`, data)
}

/**
 * 获取API测试场景详情
 * @param {string} projectId - 项目ID
 * @param {string} sceneId - 场景ID
 */
export const getApiTestSceneApi = (projectId, sceneId) => {
  return axios.get(`/api/project/${projectId}/api-test/scenes/${sceneId}/`)
}

/**
 * 更新API测试场景
 * @param {string} projectId - 项目ID
 * @param {string} sceneId - 场景ID
 * @param {object} data - 场景数据
 */
export const updateApiTestSceneApi = (projectId, sceneId, data) => {
  return axios.put(`/api/project/${projectId}/api-test/scenes/${sceneId}/`, data)
}

/**
 * 删除API测试场景
 * @param {string} projectId - 项目ID
 * @param {string} sceneId - 场景ID
 */
export const deleteApiTestSceneApi = (projectId, sceneId) => {
  return axios.delete(`/api/project/${projectId}/api-test/scenes/${sceneId}/`)
}

/**
 * 复制API测试场景
 * @param {string} projectId - 项目ID
 * @param {string} sceneId - 场景ID
 * @param {object} data - 复制参数
 */
export const copyApiTestSceneApi = (projectId, sceneId, data = {}) => {
  return axios.post(`/api/project/${projectId}/api-test/scenes/${sceneId}/copy/`, data)
}

// ==================== 测试场景步骤管理 API ====================

/**
 * 获取场景步骤列表
 * @param {string} projectId - 项目ID
 * @param {string} sceneId - 场景ID
 * @param {string} urlArgs - URL参数
 */
export const getApiTestSceneStepsApi = (projectId, sceneId, urlArgs = '') => {
  let reqUrl = `/api/project/${projectId}/api-test/scenes/${sceneId}/steps/`
  if (urlArgs) {
    reqUrl = reqUrl + '?' + urlArgs
  }
  return axios.get(reqUrl)
}

/**
 * 创建场景步骤
 * @param {string} projectId - 项目ID
 * @param {string} sceneId - 场景ID
 * @param {object} data - 步骤数据
 */
export const createApiTestSceneStepApi = (projectId, sceneId, data) => {
  return axios.post(`/api/project/${projectId}/api-test/scenes/${sceneId}/steps/`, data)
}

/**
 * 获取场景步骤详情
 * @param {string} projectId - 项目ID
 * @param {string} sceneId - 场景ID
 * @param {string} stepId - 步骤ID
 */
export const getApiTestSceneStepApi = (projectId, sceneId, stepId) => {
  return axios.get(`/api/project/${projectId}/api-test/scenes/${sceneId}/steps/${stepId}/`)
}

/**
 * 更新场景步骤
 * @param {string} projectId - 项目ID
 * @param {string} sceneId - 场景ID
 * @param {string} stepId - 步骤ID
 * @param {object} data - 步骤数据
 */
export const updateApiTestSceneStepApi = (projectId, sceneId, stepId, data) => {
  return axios.put(`/api/project/${projectId}/api-test/scenes/${sceneId}/steps/${stepId}/`, data)
}

/**
 * 删除场景步骤
 * @param {string} projectId - 项目ID
 * @param {string} sceneId - 场景ID
 * @param {string} stepId - 步骤ID
 */
export const deleteApiTestSceneStepApi = (projectId, sceneId, stepId) => {
  return axios.delete(`/api/project/${projectId}/api-test/scenes/${sceneId}/steps/${stepId}/`)
}

/**
 * 批量更新步骤顺序
 * @param {string} projectId - 项目ID
 * @param {string} sceneId - 场景ID
 * @param {object} data - 步骤顺序数据
 */
export const updateSceneStepsOrderApi = (projectId, sceneId, data) => {
  return axios.put(`/api/project/${projectId}/api-test/scenes/${sceneId}/steps/order/`, data)
}

// ==================== 测试场景执行 API ====================

/**
 * 执行API测试场景
 * @param {string} projectId - 项目ID
 * @param {string} sceneId - 场景ID
 * @param {object} data - 执行参数
 */
export const executeApiTestSceneApi = (projectId, sceneId, data = {}) => {
  return axios.post(`/api/project/${projectId}/api-test/scenes/${sceneId}/execute/`, data)
}

/**
 * 停止场景执行
 * @param {string} projectId - 项目ID
 * @param {string} sceneId - 场景ID
 * @param {string} historyId - 执行历史ID
 */
export const stopApiTestSceneApi = (projectId, sceneId, historyId) => {
  return axios.post(`/api/project/${projectId}/api-test/scenes/${sceneId}/stop/`, { history_id: historyId })
}

// ==================== 测试场景执行历史 API ====================

/**
 * 获取场景执行历史列表
 * @param {string} projectId - 项目ID
 * @param {string} urlArgs - URL参数
 */
export const getApiTestSceneHistoryApi = (projectId, urlArgs = '') => {
  let reqUrl = `/api/project/${projectId}/api-test/scenes/history/`
  if (urlArgs) {
    reqUrl = reqUrl + '?' + urlArgs
  }
  return axios.get(reqUrl)
}

/**
 * 获取场景执行历史详情
 * @param {string} projectId - 项目ID
 * @param {string} historyId - 历史ID
 */
export const getApiTestSceneHistoryDetailApi = (projectId, historyId) => {
  return axios.get(`/api/project/${projectId}/api-test/scenes/history/${historyId}/`)
}

/**
 * 获取场景步骤执行记录
 * @param {string} projectId - 项目ID
 * @param {string} historyId - 历史ID
 * @param {string} urlArgs - URL参数
 */
export const getApiTestSceneStepRecordsApi = (projectId, historyId, urlArgs = '') => {
  let reqUrl = `/api/project/${projectId}/api-test/scenes/history/${historyId}/steps/`
  if (urlArgs) {
    reqUrl = reqUrl + '?' + urlArgs
  }
  return axios.get(reqUrl)
}

// ==================== 工具函数 ====================

/**
 * 构建URL参数字符串
 * @param {object} params - 参数对象
 * @returns {string} URL参数字符串
 */
export const buildUrlParams = (params) => {
  const urlParams = new URLSearchParams()
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
      urlParams.append(key, params[key])
    }
  })
  return urlParams.toString()
}

/**
 * 处理API响应错误
 * @param {object} error - 错误对象
 * @returns {string} 错误消息
 */
export const handleApiError = (error) => {
  if (error.response) {
    // 服务器响应了错误状态码
    const { status, data } = error.response
    if (data && data.msg) {
      return data.msg
    }
    switch (status) {
      case 400:
        return '请求参数错误'
      case 401:
        return '未授权访问'
      case 403:
        return '禁止访问'
      case 404:
        return '资源不存在'
      case 500:
        return '服务器内部错误'
      default:
        return `请求失败 (${status})`
    }
  } else if (error.request) {
    // 请求已发出但没有收到响应
    return '网络连接失败'
  } else {
    // 其他错误
    return error.message || '未知错误'
  }
}

/**
 * 格式化响应数据
 * @param {object} response - 响应对象
 * @returns {object} 格式化后的数据
 */
export const formatApiResponse = (response) => {
  if (response && response.data) {
    const { code, msg, result } = response.data
    return {
      success: code === 200,
      message: msg,
      result: result
    }
  }
  return {
    success: false,
    message: '响应格式错误',
    result: null
  }
}
